# 修复PPTX文件上传问题

## 问题描述
上传PPTX文件时出现错误：`No module named 'aspose'`

## 解决方案

### 方案1：安装Aspose库（推荐）

1. **进入RAGFlow容器或Python环境**：
```bash
# 如果使用Docker
docker exec -it ragflow-container bash

# 或者激活RAGFlow的Python环境
source /path/to/ragflow/venv/bin/activate
```

2. **安装aspose-slides库**：
```bash
pip install aspose-slides
```

3. **或者安装完整的aspose套件**：
```bash
pip install aspose-slides-python
# 或者
pip install aspose.slides
```

### 方案2：检查RAGFlow配置

1. **检查RAGFlow的requirements.txt**：
```bash
cd /path/to/ragflow
cat requirements.txt | grep aspose
```

2. **如果没有aspose依赖，添加到requirements.txt**：
```txt
aspose-slides-python>=23.1
```

3. **重新安装依赖**：
```bash
pip install -r requirements.txt
```

### 方案3：使用替代解析器

如果无法安装aspose，可以配置RAGFlow使用其他PowerPoint解析器：

1. **检查可用的解析器**：
   - python-pptx
   - pptx2txt
   - 或其他兼容的解析器

2. **修改RAGFlow配置**，使用替代解析器处理PPTX文件

### 方案4：Docker环境修复

如果使用Docker部署，需要重新构建镜像：

1. **修改Dockerfile**，添加aspose依赖：
```dockerfile
RUN pip install aspose-slides-python
```

2. **重新构建镜像**：
```bash
docker build -t ragflow:latest .
```

3. **重启容器**：
```bash
docker-compose down
docker-compose up -d
```

## 验证修复

1. **检查模块是否安装成功**：
```python
python -c "import aspose.slides; print('Aspose installed successfully')"
```

2. **重启RAGFlow服务**：
```bash
# 重启服务
systemctl restart ragflow
# 或者重启Docker容器
docker-compose restart
```

3. **测试PPTX文件上传**：
   - 尝试上传之前失败的PPTX文件
   - 检查是否能正常解析

## 预防措施

1. **更新requirements.txt**：
   确保所有必要的依赖都在requirements.txt中

2. **定期检查依赖**：
   ```bash
   pip check
   ```

3. **备份工作环境**：
   在修改前备份当前的Python环境

## 常见问题

### Q: 安装aspose后仍然报错？
A: 可能需要重启RAGFlow服务，确保新安装的模块被加载

### Q: aspose库很大，有替代方案吗？
A: 可以尝试使用python-pptx库，但可能需要修改RAGFlow的解析代码

### Q: 为什么之前能上传PPTX，现在不行了？
A: 可能是：
- RAGFlow更新后依赖发生变化
- Python环境被重置
- 容器重新部署时丢失了某些依赖

## 长期解决方案

建议将aspose依赖添加到RAGFlow的官方requirements.txt中，确保在部署时自动安装所有必要的依赖。
