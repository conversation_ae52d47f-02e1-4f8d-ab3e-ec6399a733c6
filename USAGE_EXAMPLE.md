# 使用示例：新的左右分栏查看结果功能

## 1. 在父组件中使用 DocumentList

```vue
<template>
  <div class="knowledge-base-management">
    <!-- 文档列表组件 -->
    <DocumentList
      :knowledge-base-id="currentKnowledgeBaseId"
      @document-action="handleDocumentAction"
    />
    
    <!-- 解析状态组件（包含新的左右分栏查看结果功能） -->
    <DocumentParseStatus
      :knowledge-base-id="currentKnowledgeBaseId"
      :documents="documents"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DocumentList from '@/components/FileManagement/DocumentList.vue';
import DocumentParseStatus from '@/components/FileManagement/DocumentParseStatus.vue';
import type { DocumentInfo } from '@/api/iot/document';

const currentKnowledgeBaseId = ref('your-kb-id');
const documents = ref<DocumentInfo[]>([]);

// 处理文档操作事件
const handleDocumentAction = (action: string, document: DocumentInfo) => {
  switch (action) {
    case 'viewResult':
      // 处理查看结果操作
      console.log('查看文档解析结果:', document.name);
      // 可以在这里打开 DocumentParseStatus 组件的查看结果弹窗
      // 或者导航到专门的结果查看页面
      break;
    case 'edit':
      console.log('编辑文档:', document.name);
      break;
    case 'delete':
      console.log('删除文档:', document.name);
      break;
    // 其他操作...
  }
};
</script>
```

## 2. 新功能特性

### 左右分栏布局
- **左侧面板**: 显示原始文档预览，帮助用户了解上下文
- **右侧面板**: 显示文档分块列表，支持编辑和管理

### 交互功能
- **块选择**: 点击右侧的分块可以高亮显示，便于与左侧文档内容对比
- **独立滚动**: 左右两侧可以独立滚动，提高浏览效率
- **响应式设计**: 在不同屏幕尺寸下自动调整布局

### 编辑功能
- **实时编辑**: 保持原有的分块内容编辑功能
- **自动保存**: 编辑后2秒自动保存
- **批量操作**: 支持批量选择、删除分块

## 3. 样式定制

如果需要自定义样式，可以覆盖以下 CSS 类：

```css
/* 调整分栏比例 */
.split-layout .left-panel {
  flex: 1.2; /* 左侧稍大 */
}

.split-layout .right-panel {
  flex: 0.8; /* 右侧稍小 */
}

/* 自定义选中块的样式 */
.chunk-item.chunk-active {
  border-color: #your-color;
  background: #your-background;
}

/* 自定义面板头部样式 */
.panel-header {
  background: #your-header-bg;
}
```

## 4. 响应式断点

- **大屏幕 (>1200px)**: 左右分栏布局
- **中等屏幕 (768px-1200px)**: 上下布局，每个面板高度400px
- **小屏幕 (<768px)**: 紧凑的上下布局，每个面板高度300px

## 5. 事件处理

### DocumentList 组件新增事件
```typescript
// 新增的查看结果事件
emit('documentAction', 'viewResult', document);
```

### DocumentParseStatus 组件功能
- 块选择和高亮
- 文档预览刷新
- 保持所有原有的编辑功能

## 6. 兼容性说明

- 完全向后兼容，不影响现有功能
- 所有原有的 API 和事件保持不变
- 新增功能为可选使用，不会破坏现有代码
